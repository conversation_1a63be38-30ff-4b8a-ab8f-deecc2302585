/*
 * Power Consumption Comparison Example
 * 
 * This example demonstrates the power consumption difference between
 * the original implementation and the low-power optimized version.
 * 
 * Use this with a current meter to measure actual power consumption.
 */

#include <Arduino.h>
#include <esp_sleep.h>
#include <esp_wifi.h>
#include <esp_bt.h>
#include <driver/adc.h>
#include <soc/rtc_cntl_reg.h>
#include <soc/sens_reg.h>

// Configuration
#define TEST_DURATION_MS 10000  // 10 seconds per test
#define MEASUREMENT_PIN 2       // LED pin to indicate test phases

enum TestMode {
  MODE_ORIGINAL_SIMULATION,     // Simulate original high-power mode
  MODE_OPTIMIZED_ACTIVE,        // Optimized active mode
  MODE_OPTIMIZED_SLEEP,         // Optimized sleep mode
  MODE_COMPLETE
};

TestMode currentMode = MODE_ORIGINAL_SIMULATION;
unsigned long testStartTime = 0;

void setup() {
  Serial.begin(115200);
  pinMode(MEASUREMENT_PIN, OUTPUT);
  
  Serial.println("\n=== Power Consumption Comparison Test ===");
  Serial.println("Connect current meter to measure power consumption");
  Serial.println("LED indicates current test phase");
  Serial.println();
  
  delay(3000); // Allow time to connect measurement equipment
  
  testStartTime = millis();
  startCurrentTest();
}

void loop() {
  unsigned long elapsed = millis() - testStartTime;
  
  if (elapsed >= TEST_DURATION_MS) {
    // Move to next test
    currentMode = (TestMode)((int)currentMode + 1);
    
    if (currentMode == MODE_COMPLETE) {
      Serial.println("All tests complete!");
      Serial.println("Results summary:");
      Serial.println("1. Original simulation: High power consumption");
      Serial.println("2. Optimized active: Reduced power consumption");
      Serial.println("3. Optimized sleep: Minimal power consumption");
      
      // Stay in low power mode
      enterDeepSleep();
    } else {
      testStartTime = millis();
      startCurrentTest();
    }
  }
  
  // Execute current test behavior
  switch (currentMode) {
    case MODE_ORIGINAL_SIMULATION:
      simulateOriginalMode();
      break;
      
    case MODE_OPTIMIZED_ACTIVE:
      simulateOptimizedActive();
      break;
      
    case MODE_OPTIMIZED_SLEEP:
      simulateOptimizedSleep();
      break;
      
    default:
      break;
  }
}

void startCurrentTest() {
  switch (currentMode) {
    case MODE_ORIGINAL_SIMULATION:
      Serial.println("TEST 1: Simulating original high-power mode");
      Serial.println("- WiFi/BT enabled");
      Serial.println("- Continuous polling");
      Serial.println("- All peripherals active");
      digitalWrite(MEASUREMENT_PIN, HIGH); // LED on for high power
      
      // Enable power-hungry features
      esp_wifi_start();
      esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT);
      break;
      
    case MODE_OPTIMIZED_ACTIVE:
      Serial.println("TEST 2: Optimized active mode");
      Serial.println("- WiFi/BT disabled");
      Serial.println("- Efficient processing");
      Serial.println("- Minimal peripheral usage");
      digitalWrite(MEASUREMENT_PIN, LOW); // LED off
      
      // Disable power-hungry features
      esp_wifi_stop();
      esp_bt_controller_disable();
      disableUnusedPeripherals();
      break;
      
    case MODE_OPTIMIZED_SLEEP:
      Serial.println("TEST 3: Optimized sleep mode");
      Serial.println("- Deep sleep simulation");
      Serial.println("- Minimal power consumption");
      Serial.println("- Wake-up capability maintained");
      digitalWrite(MEASUREMENT_PIN, LOW);
      
      // Prepare for sleep mode
      esp_wifi_stop();
      esp_bt_controller_disable();
      disableUnusedPeripherals();
      break;
      
    default:
      break;
  }
  
  Serial.printf("Measure current consumption for %d seconds...\n", TEST_DURATION_MS / 1000);
  Serial.println();
}

void simulateOriginalMode() {
  // Simulate continuous polling and processing
  for (int i = 0; i < 100; i++) {
    // Simulate I2C communication
    delayMicroseconds(100);
    
    // Simulate processing
    volatile int dummy = 0;
    for (int j = 0; j < 1000; j++) {
      dummy += j;
    }
  }
  
  // Blink LED to show activity
  static unsigned long lastBlink = 0;
  if (millis() - lastBlink > 100) {
    digitalWrite(MEASUREMENT_PIN, !digitalRead(MEASUREMENT_PIN));
    lastBlink = millis();
  }
  
  delay(1); // Minimal delay
}

void simulateOptimizedActive() {
  // Simulate efficient card reading process
  static unsigned long lastActivity = 0;
  
  if (millis() - lastActivity > 1000) {
    // Simulate card read every second
    Serial.println("Simulating card read...");
    
    // Brief activity burst
    for (int i = 0; i < 10; i++) {
      delayMicroseconds(100);
    }
    
    lastActivity = millis();
  }
  
  // Most time spent in low-power delay
  delay(50);
}

void simulateOptimizedSleep() {
  // Simulate sleep mode with periodic wake-up
  static unsigned long lastWakeup = 0;
  
  if (millis() - lastWakeup > 2000) {
    Serial.println("Simulating wake-up from sleep...");
    
    // Brief wake-up activity
    delay(100);
    
    Serial.println("Returning to sleep simulation...");
    lastWakeup = millis();
  }
  
  // Simulate deep sleep with very low activity
  delay(100);
}

void disableUnusedPeripherals() {
  // Disable ADC (ESP32 specific)
  #ifdef ESP32
    adc_power_release();
    // Alternative method for older ESP-IDF versions
    CLEAR_PERI_REG_MASK(SENS_SAR_MEAS_WAIT2_REG, SENS_FORCE_XPD_SAR_M);
    CLEAR_PERI_REG_MASK(SENS_SAR_READ_CTRL_REG, SENS_SAR1_DIG_FORCE_M);
    CLEAR_PERI_REG_MASK(SENS_SAR_READ_CTRL2_REG, SENS_SAR2_DIG_FORCE_M);
  #endif

  // Set unused pins to input with pullup
  for (int i = 0; i < 40; i++) {
    if (i != MEASUREMENT_PIN && i != 1 && i != 3) { // Skip LED and Serial pins
      pinMode(i, INPUT_PULLUP);
    }
  }
}

void enterDeepSleep() {
  Serial.println("Entering deep sleep mode...");
  Serial.println("Reset to restart tests");
  Serial.flush();
  
  // Configure wake-up (timer only for this demo)
  esp_sleep_enable_timer_wakeup(60000000ULL); // 60 seconds
  
  delay(100);
  esp_deep_sleep_start();
}
