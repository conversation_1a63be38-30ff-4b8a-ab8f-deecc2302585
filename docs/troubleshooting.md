# 故障排除指南

## I2C错误 263 问题分析

### 错误现象
```
[  6273][E][Wire.cpp:513] requestFrom(): i2cRead returned Error 263
Failed to start NFC detection
Failed to read card UID
```

### 可能原因

#### 1. 硬件连接问题
- **检查接线**：确保SDA、SCL、VCC、GND连接正确
- **检查电源**：PN532需要3.3V电源，确保电压稳定
- **检查IRQ引脚**：GPIO34连接到PN532的IRQ引脚
- **检查RESET引脚**：GPIO5连接到PN532的RESET引脚

#### 2. I2C总线问题
- **上拉电阻**：I2C总线需要上拉电阻（通常4.7kΩ）
- **总线冲突**：确保I2C总线上只有一个设备
- **线缆长度**：I2C线缆不宜过长，建议小于30cm

#### 3. PN532模块问题
- **模式设置**：确保PN532设置为I2C模式
- **模块故障**：PN532模块可能损坏

### 解决方案

#### 1. 硬件检查清单
```
□ VCC -> 3.3V
□ GND -> GND  
□ SDA -> GPIO21 (默认)
□ SCL -> GPIO22 (默认)
□ IRQ -> GPIO34
□ RST -> GPIO5
□ 上拉电阻已安装
□ 连接牢固无松动
```

#### 2. 软件改进
代码已添加以下改进：

**重试机制**：
- NFC初始化重试5次
- 检测启动重试3次
- 自动错误恢复

**健康检查**：
- 连续错误计数
- 自动重新初始化
- 错误状态监控

**调试信息**：
- 详细的状态日志
- 错误计数显示
- 操作成功确认

#### 3. 测试步骤

**步骤1：基础连接测试**
```cpp
// 在setup()中添加I2C扫描代码
Wire.begin();
Serial.println("Scanning I2C bus...");
for(byte i = 1; i < 127; i++) {
  Wire.beginTransmission(i);
  if(Wire.endTransmission() == 0) {
    Serial.print("Found device at 0x");
    Serial.println(i, HEX);
  }
}
```

**步骤2：PN532地址确认**
- PN532的I2C地址通常是0x24
- 如果扫描不到设备，检查硬件连接

**步骤3：逐步测试**
1. 先测试基础通信（getFirmwareVersion）
2. 再测试SAM配置
3. 最后测试卡片检测

### 常见问题解答

#### Q: 为什么会出现"BBA952CA"这样的UID？
A: 这可能是：
- 内存中的随机数据
- 之前检测的残留数据
- 通信错误导致的错误数据

#### Q: 为什么startPassiveTargetIDDetection失败？
A: 可能原因：
- PN532未正确初始化
- I2C通信异常
- 模块处于错误状态

#### Q: 如何判断是硬件还是软件问题？
A: 测试方法：
1. 使用万用表测试电压
2. 使用示波器检查I2C信号
3. 尝试其他I2C设备
4. 使用官方示例代码测试

### 调试建议

#### 1. 增加调试输出
在关键位置添加Serial.println()：
- NFC初始化前后
- 每次I2C操作前后
- 状态转换时

#### 2. 降低操作频率
- 增加delay时间
- 减少检测频率
- 避免连续操作

#### 3. 使用看门狗
- 监控系统状态
- 自动重启恢复
- 记录错误日志

### 硬件替代方案

如果问题持续存在：

#### 1. 更换PN532模块
- 尝试不同厂商的模块
- 检查模块版本兼容性

#### 2. 使用SPI接口
- 修改为SPI通信
- 可能更稳定

#### 3. 添加硬件滤波
- 在电源线添加电容
- 使用屏蔽线缆
- 添加共模扼流圈

### 监控和维护

#### 1. 错误统计
代码已添加错误计数功能：
- consecutiveErrors：连续错误次数
- lastSuccessfulOperation：最后成功操作时间

#### 2. 自动恢复
- 超过5次连续错误自动重新初始化
- 定期健康检查
- 状态重置机制

#### 3. 预防措施
- 定期重启系统
- 监控电源质量
- 记录运行日志
