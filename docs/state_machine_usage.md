# 状态机门禁系统使用说明

## 概述

这是一个基于ESP32和PN532 NFC模块的智能门禁系统，使用状态机架构实现非阻塞式卡片检测和鉴权。

## 主要特性

### 1. 自动卡片检测和鉴权
- 系统在主循环中持续监听NFC卡片
- 检测到卡片后自动进行鉴权
- 无需手动输入命令进行鉴权

### 2. 智能冷却机制
- 鉴权完成后进入冷却期（1秒）
- 冷却期内检测到相同卡片：重置冷却计时器，防止重复触发
- 冷却期内检测到不同卡片：立即进行鉴权，支持快速换卡

### 3. 非阻塞注册模式
- 注册模式有10秒超时
- 超时后自动返回正常检测模式
- 注册过程中可以随时取消

## 状态机说明

### 状态列表
1. **WAITING_FOR_CARD** - 等待卡片检测
2. **AUTHENTICATING** - 正在进行鉴权
3. **COOLDOWN** - 冷却期
4. **REGISTRATION_MODE** - 注册模式（等待卡片）
5. **REGISTERING** - 正在注册卡片

### 状态转换
```
WAITING_FOR_CARD → 检测到卡片 → AUTHENTICATING
AUTHENTICATING → 鉴权完成 → COOLDOWN
COOLDOWN → 超时或检测到不同卡片 → WAITING_FOR_CARD/AUTHENTICATING
REGISTRATION_MODE → 检测到卡片 → REGISTERING
REGISTRATION_MODE → 超时10秒 → WAITING_FOR_CARD
REGISTERING → 注册完成 → WAITING_FOR_CARD
```

## 可用命令

### `reg` - 注册新卡片
- 激活注册模式
- 10秒内将空白卡片贴近读卡器
- 系统自动生成随机密钥并写入卡片
- 将卡片信息保存到数据库

### `list` - 列出所有已注册卡片
- 显示所有已注册卡片的UID和密钥

### `del <UID>` - 删除指定卡片
- 从数据库中删除指定UID的卡片
- 例如：`del 04A1B2C3`

## LED指示

### 鉴权结果
- **成功**：快速闪烁3次（100ms间隔）
- **失败**：慢速闪烁1次（500ms）

### 注册结果
- **成功**：快速闪烁5次（100ms间隔）

### 系统启动
- 启动时闪烁3次表示系统就绪

## 时间参数

- **冷却期**：1000ms（1秒）
- **注册超时**：10000ms（10秒）
- **相同卡片延迟**：100ms

## 使用示例

### 1. 注册新卡片
```
> reg
-- Registration mode activated --
-- Tap blank card within 10 seconds --
Card detected for registration!
Registering card with UID: 04A1B2C3
Card registered successfully
```

### 2. 自动鉴权
```
Card detected!
Authenticating card: 04A1B2C3
Authentication successful
```

### 3. 冷却期行为
```
Card detected!
Authenticating card: 04A1B2C3
Authentication successful
Same card detected during cooldown, resetting timer
Different card detected, bypassing cooldown
Authenticating card: 04D5E6F7
```

## 硬件连接

- **PN532_IRQ**: GPIO 34
- **PN532_RESET**: GPIO 5
- **LED**: GPIO 2
- **I2C**: 使用默认SDA/SCL引脚

## 注意事项

1. 系统启动后会自动进入卡片检测模式
2. 鉴权是完全自动的，无需手动命令
3. 冷却机制确保同一张卡不会重复触发
4. 换卡时可以立即响应，无需等待冷却结束
5. 注册模式有超时保护，避免系统卡死
