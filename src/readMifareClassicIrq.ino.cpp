# 1 "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpqywlzw_m"
#include <Arduino.h>
# 1 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq.ino"
# 43 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq.ino"
#include <Wire.h>
#include <SPI.h>
#include <Adafruit_PN532.h>


#define PN532_SCK (2)
#define PN532_MOSI (3)
#define PN532_SS (4)
#define PN532_MISO (5)



#define PN532_IRQ 34
#define PN532_RESET 5

const int DELAY_BETWEEN_CARDS = 500;
long timeLastCardRead = 0;
boolean readerDisabled = false;
int irqCurr;
int irqPrev;


Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);
void setup(void);
void loop(void);
void startListeningToNFC();
void handleCardDetected();
#line 67 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq.ino"
void setup(void) {
  Serial.begin(115200);
  while (!Serial) delay(10);

  Serial.println("Hello!");

  nfc.begin();

  uint32_t versiondata = nfc.getFirmwareVersion();
  if (! versiondata) {
    Serial.print("Didn't find PN53x board");
    while (1);
  }

  Serial.print("Found chip PN5"); Serial.println((versiondata>>24) & 0xFF, HEX);
  Serial.print("Firmware ver. "); Serial.print((versiondata>>16) & 0xFF, DEC);
  Serial.print('.'); Serial.println((versiondata>>8) & 0xFF, DEC);

  startListeningToNFC();
}

void loop(void) {
  if (readerDisabled) {
    if (millis() - timeLastCardRead > DELAY_BETWEEN_CARDS) {
      readerDisabled = false;
      startListeningToNFC();
    }
  } else {
    irqCurr = digitalRead(PN532_IRQ);


    if (irqCurr == LOW && irqPrev == HIGH) {
       Serial.println("Got NFC IRQ");
       handleCardDetected();
    }

    irqPrev = irqCurr;
  }
}

void startListeningToNFC() {

  irqPrev = irqCurr = HIGH;

  Serial.println("Starting passive read for an ISO14443A Card ...");
  if (!nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A)) {
    Serial.println("No card found. Waiting...");
  } else {
    Serial.println("Card already present.");
    handleCardDetected();
  }
}

void handleCardDetected() {
    uint8_t success = false;
    uint8_t uid[] = { 0, 0, 0, 0, 0, 0, 0 };
    uint8_t uidLength;


    success = nfc.readDetectedPassiveTargetID(uid, &uidLength);
    Serial.println(success ? "Read successful" : "Read failed (not a card?)");

    if (success) {

      Serial.println("Found an ISO14443A card");
      Serial.print("  UID Length: ");Serial.print(uidLength, DEC);Serial.println(" bytes");
      Serial.print("  UID Value: ");
      nfc.PrintHex(uid, uidLength);

      if (uidLength == 4)
      {

        uint32_t cardid = uid[0];
        cardid <<= 8;
        cardid |= uid[1];
        cardid <<= 8;
        cardid |= uid[2];
        cardid <<= 8;
        cardid |= uid[3];
        Serial.print("Seems to be a Mifare Classic card #");
        Serial.println(cardid);
      }
      Serial.println("");

      timeLastCardRead = millis();
    }


    readerDisabled = true;
}