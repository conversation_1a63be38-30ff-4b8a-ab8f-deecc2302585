# 1 "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpo4azutbo"
#include <Arduino.h>
# 1 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq_demo.ino"
# 32 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq_demo.ino"
#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <Adafruit_PN532.h>
#include <esp_sleep.h>
#include <esp_wifi.h>
#include <esp_bt.h>
#include <driver/adc.h>
#include <soc/rtc_cntl_reg.h>
#include <soc/sens_reg.h>






#define PN532_IRQ 34
#define PN532_RESET 5


#define CARD_READ_TIMEOUT_MS 5000
#define SLEEP_AFTER_CARD_MS 2000
#define DEEP_SLEEP_DURATION_US 30000000ULL
#define MAX_ACTIVE_TIME_MS 10000


#define ENABLE_SERIAL_DEBUG true
#define SERIAL_BAUD_RATE 115200





Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);


enum PowerState {
  STATE_INITIALIZING,
  STATE_WAITING_FOR_CARD,
  STATE_CARD_DETECTED,
  STATE_PROCESSING_CARD,
  STATE_SLEEP_DELAY,
  STATE_ENTERING_SLEEP
};

PowerState currentState = STATE_INITIALIZING;
unsigned long stateStartTime = 0;
unsigned long lastActivityTime = 0;
bool cardPresent = false;


RTC_DATA_ATTR int bootCount = 0;
void disableUnusedPeripherals();
void enterDeepSleep();
void printWakeupReason();
void setup(void);
bool initializeNFC();
bool startListeningToNFC();
void loop(void);
void handleWaitingForCard();
void handleCardDetected();
void handleProcessingCard();
void handleSleepDelay();
#line 89 "C:/Users/<USER>/CLionProjects/PN532_Demo_I2C/src/readMifareClassicIrq_demo.ino"
void disableUnusedPeripherals() {

  esp_wifi_stop();
  esp_bt_controller_disable();


  #ifdef ESP32
    adc_power_release();

    CLEAR_PERI_REG_MASK(SENS_SAR_MEAS_WAIT2_REG, SENS_FORCE_XPD_SAR_M);
    CLEAR_PERI_REG_MASK(SENS_SAR_READ_CTRL_REG, SENS_SAR1_DIG_FORCE_M);
    CLEAR_PERI_REG_MASK(SENS_SAR_READ_CTRL2_REG, SENS_SAR2_DIG_FORCE_M);
  #endif


  for (int i = 0; i < 40; i++) {
    if (i != PN532_IRQ && i != PN532_RESET && i != 21 && i != 22 && i != 1 && i != 3) {

      pinMode(i, INPUT_PULLUP);
    }
  }
}

void enterDeepSleep() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Entering deep sleep...");
    Serial.flush();
  }


  esp_sleep_enable_ext0_wakeup((gpio_num_t)PN532_IRQ, 0);


  esp_sleep_enable_timer_wakeup(DEEP_SLEEP_DURATION_US);




  delay(100);
  esp_deep_sleep_start();
}

void printWakeupReason() {
  if (!ENABLE_SERIAL_DEBUG) return;

  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  switch(wakeup_reason) {
    case ESP_SLEEP_WAKEUP_EXT0:
      Serial.println("Wakeup: NFC card detected (IRQ)");
      break;
    case ESP_SLEEP_WAKEUP_TIMER:
      Serial.println("Wakeup: Timer timeout");
      break;
    case ESP_SLEEP_WAKEUP_UNDEFINED:
    default:
      Serial.println("Wakeup: Power on reset");
      break;
  }
}

void setup(void) {

  ++bootCount;


  if (ENABLE_SERIAL_DEBUG) {
    Serial.begin(SERIAL_BAUD_RATE);

    if (bootCount == 1) {
      while (!Serial && millis() < 3000) delay(10);
    }

    Serial.println("\n=== Low Power NFC Reader ===");
    Serial.printf("Boot count: %d\n", bootCount);
    printWakeupReason();
  }


  disableUnusedPeripherals();


  stateStartTime = millis();
  lastActivityTime = millis();


  if (!initializeNFC()) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("NFC initialization failed - entering sleep");
    }
    delay(1000);
    enterDeepSleep();
  }


  currentState = STATE_WAITING_FOR_CARD;
  stateStartTime = millis();

  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Ready - waiting for NFC card...");
  }
}





bool initializeNFC() {
  nfc.begin();

  uint32_t versiondata = nfc.getFirmwareVersion();
  if (!versiondata) {
    return false;
  }

  if (ENABLE_SERIAL_DEBUG) {
    Serial.printf("Found PN532 chip: PN5%02X\n", (versiondata >> 24) & 0xFF);
    Serial.printf("Firmware version: %d.%d\n",
                  (versiondata >> 16) & 0xFF,
                  (versiondata >> 8) & 0xFF);
  }


  nfc.SAMConfig();

  return true;
}

bool startListeningToNFC() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Starting NFC detection...");
  }


  bool result = nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A);

  if (result && ENABLE_SERIAL_DEBUG) {
    Serial.println("Card already present");
  }

  return result;
}





void loop(void) {
  unsigned long currentTime = millis();
  unsigned long stateElapsed = currentTime - stateStartTime;
  unsigned long totalActiveTime = currentTime - lastActivityTime;


  if (totalActiveTime > MAX_ACTIVE_TIME_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Max active time exceeded - forcing sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }

  switch (currentState) {
    case STATE_WAITING_FOR_CARD:
      handleWaitingForCard();
      break;

    case STATE_CARD_DETECTED:
      handleCardDetected();
      break;

    case STATE_PROCESSING_CARD:
      handleProcessingCard();
      break;

    case STATE_SLEEP_DELAY:
      handleSleepDelay();
      break;

    case STATE_ENTERING_SLEEP:
      enterDeepSleep();
      break;

    default:
      currentState = STATE_WAITING_FOR_CARD;
      stateStartTime = currentTime;
      break;
  }


  delay(10);
}





void handleWaitingForCard() {
  unsigned long currentTime = millis();
  unsigned long stateElapsed = currentTime - stateStartTime;


  if (digitalRead(PN532_IRQ) == LOW) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("IRQ detected - card present");
    }
    currentState = STATE_CARD_DETECTED;
    stateStartTime = currentTime;
    return;
  }


  if (stateElapsed > 100) {
    if (startListeningToNFC()) {
      currentState = STATE_CARD_DETECTED;
      stateStartTime = currentTime;
      return;
    }
  }


  if (stateElapsed > CARD_READ_TIMEOUT_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("No card detected - entering sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }
}

void handleCardDetected() {
  if (ENABLE_SERIAL_DEBUG) {
    Serial.println("Processing detected card...");
  }

  currentState = STATE_PROCESSING_CARD;
  stateStartTime = millis();
}

void handleProcessingCard() {
  uint8_t success = false;
  uint8_t uid[] = { 0, 0, 0, 0, 0, 0, 0 };
  uint8_t uidLength;


  success = nfc.readDetectedPassiveTargetID(uid, &uidLength);

  if (ENABLE_SERIAL_DEBUG) {
    Serial.println(success ? "Card read successful" : "Card read failed");
  }

  if (success) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Found ISO14443A card");
      Serial.printf("  UID Length: %d bytes\n", uidLength);
      Serial.print("  UID Value: ");
      nfc.PrintHex(uid, uidLength);

      if (uidLength == 4) {
        uint32_t cardid = uid[0];
        cardid <<= 8;
        cardid |= uid[1];
        cardid <<= 8;
        cardid |= uid[2];
        cardid <<= 8;
        cardid |= uid[3];
        Serial.printf("Mifare Classic card ID: %lu\n", cardid);
      }
      Serial.println();
    }
  }


  currentState = STATE_SLEEP_DELAY;
  stateStartTime = millis();
}

void handleSleepDelay() {
  unsigned long stateElapsed = millis() - stateStartTime;

  if (stateElapsed > SLEEP_AFTER_CARD_MS) {
    if (ENABLE_SERIAL_DEBUG) {
      Serial.println("Sleep delay complete - entering sleep");
    }
    currentState = STATE_ENTERING_SLEEP;
  }
}