// I2C诊断工具 - 用于排查PN532通信问题
// 使用方法：将此文件重命名为main.cpp来替换原文件进行测试

#include <Arduino.h>
#include <Wire.h>

// PN532 I2C地址
#define PN532_I2C_ADDRESS 0x24

// GPIO引脚定义
#define SDA_PIN 21
#define SCL_PIN 22
#define PN532_IRQ 34
#define PN532_RESET 5

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=================================");
  Serial.println("    I2C诊断工具启动");
  Serial.println("=================================");
  
  // 初始化引脚
  pinMode(PN532_IRQ, INPUT);
  pinMode(PN532_RESET, OUTPUT);
  
  // 重置PN532
  Serial.println("1. 重置PN532模块...");
  digitalWrite(PN532_RESET, LOW);
  delay(100);
  digitalWrite(PN532_RESET, HIGH);
  delay(500);
  
  // 初始化I2C
  Serial.println("2. 初始化I2C总线...");
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 降低到100kHz
  
  // 扫描I2C设备
  Serial.println("3. 扫描I2C总线...");
  scanI2CDevices();
  
  // 测试PN532特定地址
  Serial.println("4. 测试PN532地址...");
  testPN532Address();
  
  // 读取IRQ引脚状态
  Serial.println("5. 检查IRQ引脚状态...");
  Serial.print("IRQ引脚状态: ");
  Serial.println(digitalRead(PN532_IRQ) ? "HIGH" : "LOW");
  
  Serial.println("=================================");
  Serial.println("诊断完成，请检查上述结果");
  Serial.println("=================================");
}

void loop() {
  // 持续监控IRQ引脚
  static int lastIRQ = -1;
  int currentIRQ = digitalRead(PN532_IRQ);
  
  if (currentIRQ != lastIRQ) {
    Serial.print("IRQ状态变化: ");
    Serial.println(currentIRQ ? "HIGH" : "LOW");
    lastIRQ = currentIRQ;
  }
  
  delay(100);
}

void scanI2CDevices() {
  byte error, address;
  int deviceCount = 0;
  
  Serial.println("扫描地址范围: 0x01 - 0x7F");
  
  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();
    
    if (error == 0) {
      Serial.print("发现I2C设备，地址: 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);
      
      if (address == PN532_I2C_ADDRESS) {
        Serial.print(" <- 这是PN532地址!");
      }
      Serial.println();
      deviceCount++;
    }
    else if (error == 4) {
      Serial.print("未知错误，地址: 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }
  
  if (deviceCount == 0) {
    Serial.println("❌ 未发现任何I2C设备!");
    Serial.println("请检查:");
    Serial.println("  - VCC连接到3.3V");
    Serial.println("  - GND连接正确");
    Serial.println("  - SDA连接到GPIO21");
    Serial.println("  - SCL连接到GPIO22");
    Serial.println("  - 上拉电阻(4.7kΩ)");
  } else {
    Serial.print("✅ 发现 ");
    Serial.print(deviceCount);
    Serial.println(" 个I2C设备");
  }
}

void testPN532Address() {
  Serial.print("测试PN532地址 0x");
  Serial.print(PN532_I2C_ADDRESS, HEX);
  Serial.println("...");
  
  // 测试基础通信
  Wire.beginTransmission(PN532_I2C_ADDRESS);
  byte error = Wire.endTransmission();
  
  switch(error) {
    case 0:
      Serial.println("✅ PN532响应正常");
      break;
    case 1:
      Serial.println("❌ 数据太长，缓冲区溢出");
      break;
    case 2:
      Serial.println("❌ 传输时收到NACK（地址）");
      Serial.println("   可能原因：设备不在此地址或未连接");
      break;
    case 3:
      Serial.println("❌ 传输时收到NACK（数据）");
      break;
    case 4:
      Serial.println("❌ 其他错误");
      break;
    default:
      Serial.print("❌ 未知错误代码: ");
      Serial.println(error);
      break;
  }
  
  // 尝试读取数据
  Serial.println("尝试从PN532读取数据...");
  Wire.requestFrom(PN532_I2C_ADDRESS, (uint8_t)1);
  
  if (Wire.available()) {
    byte data = Wire.read();
    Serial.print("✅ 读取到数据: 0x");
    Serial.println(data, HEX);
  } else {
    Serial.println("❌ 无法读取数据");
  }
}
