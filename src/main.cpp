// 最简化的PN532测试程序
// 用于排查硬件问题

#include <Arduino.h>
#include <Wire.h>

// PN532 I2C地址
#define PN532_I2C_ADDRESS 0x24

// GPIO引脚
#define PN532_IRQ 34
#define PN532_RESET 5

// 函数声明
void testI2CBasic();

void setup() {
  Serial.begin(115200);
  delay(3000);
  
  Serial.println("=== 最简化PN532测试 ===");
  
  // 初始化引脚
  pinMode(PN532_IRQ, INPUT);
  pinMode(PN532_RESET, OUTPUT);
  
  // 硬件重置
  Serial.println("1. 硬件重置PN532...");
  digitalWrite(PN532_RESET, LOW);
  delay(100);
  digitalWrite(PN532_RESET, HIGH);
  delay(1000);
  
  // 检查IRQ状态
  Serial.print("2. IRQ引脚状态: ");
  Serial.println(digitalRead(PN532_IRQ) ? "HIGH" : "LOW");
  
  // 初始化I2C，使用最低速度
  Serial.println("3. 初始化I2C (50kHz)...");
  Wire.begin();
  Wire.setClock(50000); // 极低速度
  
  // 简单的I2C测试
  Serial.println("4. 测试I2C通信...");
  testI2CBasic();
  
  Serial.println("=== 测试完成 ===");
}

void loop() {
  // 持续监控IRQ引脚
  static int lastIRQ = -1;
  int currentIRQ = digitalRead(PN532_IRQ);
  
  if (currentIRQ != lastIRQ) {
    Serial.print("IRQ变化: ");
    Serial.println(currentIRQ ? "HIGH" : "LOW");
    lastIRQ = currentIRQ;
  }
  
  delay(100);
}

void testI2CBasic() {
  Serial.println("尝试基础I2C通信...");
  
  for (int attempt = 1; attempt <= 5; attempt++) {
    Serial.print("尝试 ");
    Serial.print(attempt);
    Serial.print(": ");
    
    Wire.beginTransmission(PN532_I2C_ADDRESS);
    byte error = Wire.endTransmission();
    
    switch(error) {
      case 0:
        Serial.println("✅ 通信成功");
        
        // 尝试读取一个字节
        Serial.print("   读取测试: ");
        Wire.requestFrom((uint8_t)PN532_I2C_ADDRESS, (uint8_t)1);
        if (Wire.available()) {
          byte data = Wire.read();
          Serial.print("读到数据 0x");
          Serial.println(data, HEX);
        } else {
          Serial.println("无数据");
        }
        return;
        
      case 1:
        Serial.println("❌ 数据太长");
        break;
      case 2:
        Serial.println("❌ 地址NACK - 设备不响应");
        break;
      case 3:
        Serial.println("❌ 数据NACK");
        break;
      case 4:
        Serial.println("❌ 其他错误");
        break;
      default:
        Serial.print("❌ 未知错误: ");
        Serial.println(error);
        break;
    }
    
    delay(1000);
  }
  
  Serial.println("❌ 所有尝试都失败了");
}
