// Door Access System using ESP32, PN532, and MIFARE Classic
// Features:
// 1. Automatic card authentication in main loop using state machine
// 2. Register blank card: generate random key, write to sector trailer (block 7), record mapping in SPIFFS JSON
// 3. List and delete cards: manage registered cards via Serial commands
// 4. Smart cooldown mechanism to prevent duplicate reads from same card

#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <Adafruit_PN532.h>

// =============================================================================
// 配置
// =============================================================================
// PN532 I2C pins
#define PN532_IRQ   34
#define PN532_RESET 5

// LED pin
#define LED_PIN 2

// MIFARE Classic settings
#define SECTOR_TRAILER_BLOCK 7
#define AUTH_BLOCK 4
#define KEY_SIZE 6
#define TRAILER_SIZE 16

// 文件系统
const char* CARD_FILE = "/cards.json";

// 时间常量
const unsigned long COOLDOWN_DURATION = 1000;      // 1秒冷却期
const unsigned long REGISTRATION_TIMEOUT = 10000;   // 10秒注册超时
const unsigned long SAME_CARD_DELAY = 100;         // 相同卡片延迟100ms

// =============================================================================
// 状态机定义
// =============================================================================
enum SystemState {
  WAITING_FOR_CARD,
  AUTHENTICATING,
  COOLDOWN,
  REGISTRATION_MODE,
  REGISTERING
};

struct CardInfo {
  String lastUID;
  unsigned long lastDetectionTime;
  unsigned long cooldownStartTime;
  unsigned long registrationStartTime;
  bool isValid;
};

// =============================================================================
// 全局变量
// =============================================================================
Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);
JsonDocument cardDatabase;
uint8_t defaultKey[KEY_SIZE] = { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };

SystemState currentState = WAITING_FOR_CARD;
CardInfo cardInfo = {"", 0, 0, 0, false};
int irqCurrent = HIGH;
int irqPrevious = HIGH;
unsigned long lastSuccessfulOperation = 0;
int consecutiveErrors = 0;

// =============================================================================
// 函数声明
// =============================================================================
bool initializeNFC();
bool checkNFCHealth();

// =============================================================================
// 状态机函数
// =============================================================================
void setState(SystemState newState) {
  Serial.print("State change: ");
  Serial.print(currentState);
  Serial.print(" -> ");
  Serial.println(newState);
  currentState = newState;
}

void startNFCDetection() {
  if (!checkNFCHealth()) {
    return;
  }

  irqPrevious = irqCurrent = HIGH;
  Serial.println("Starting NFC detection...");

  // Try multiple times if it fails
  for (int retry = 0; retry < 3; retry++) {
    if (nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A)) {
      Serial.println("NFC detection started successfully");
      consecutiveErrors = 0;
      lastSuccessfulOperation = millis();
      return;
    }
    Serial.print("Failed to start NFC detection, retry ");
    Serial.println(retry + 1);
    consecutiveErrors++;
    delay(100);
  }
  Serial.println("Failed to start NFC detection after retries");
  consecutiveErrors++;
}

// =============================================================================
// 工具函数
// =============================================================================
String uidToString(uint8_t* uid, uint8_t len) {
  String result;
  for (uint8_t i = 0; i < len; i++) {
    if (uid[i] < 0x10) result += "0";
    result += String(uid[i], HEX);
  }
  result.toUpperCase();
  return result;
}

void generateRandomKey(uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = random(0, 256);
  }
}

String keyToHexString(uint8_t* key) {
  char keyHex[13] = {0};
  for (int i = 0; i < KEY_SIZE; i++) {
    sprintf(&keyHex[i * 2], "%02X", key[i]);
  }
  return String(keyHex);
}

void hexStringToKey(const String& hexString, uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = strtoul(hexString.substring(i * 2, i * 2 + 2).c_str(), NULL, 16);
  }
}

String readCurrentCardUID() {
  uint8_t uid[7], uidLen;

  // Try to read the detected card
  if (nfc.readDetectedPassiveTargetID(uid, &uidLen)) {
    String uidStr = uidToString(uid, uidLen);
    Serial.print("Read UID: ");
    Serial.println(uidStr);
    return uidStr;
  }

  Serial.println("Failed to read detected card UID");
  return "";
}

// =============================================================================
// LED 控制
// =============================================================================
void blinkLED(int times = 3, int delayMs = 100) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

// =============================================================================
// 文件系统操作
// =============================================================================
void saveCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_WRITE);
  if (!file) {
    Serial.println("Failed to open card file for writing");
    return;
  }
  serializeJsonPretty(cardDatabase, file);
  file.close();
}

void loadCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_READ);
  if (!file) {
    // Create empty array if file doesn't exist
    cardDatabase.to<JsonArray>();
    saveCards();
  } else {
    DeserializationError err = deserializeJson(cardDatabase, file);
    if (err) {
      // Reset if file is corrupt
      Serial.println("Card database corrupt, resetting...");
      cardDatabase.to<JsonArray>();
      saveCards();
    }
    file.close();
  }
}

// =============================================================================
// NFC 操作
// =============================================================================
bool authenticateBlock(uint8_t* uid, uint8_t uidLen, uint8_t blockNumber, uint8_t* key) {
  return nfc.mifareclassic_AuthenticateBlock(uid, uidLen, blockNumber, 0, key);
}

bool writeSectorTrailer(uint8_t* newKey) {
  uint8_t trailer[TRAILER_SIZE];

  // Set new KeyA
  memcpy(trailer, newKey, KEY_SIZE);

  // Set default access bits
  memcpy(trailer + 6, "\xFF\x07\x80\x69", 4);

  // Keep default KeyB
  memcpy(trailer + 10, defaultKey, KEY_SIZE);

  return nfc.mifareclassic_WriteDataBlock(SECTOR_TRAILER_BLOCK, trailer);
}

// =============================================================================
// 卡片管理
// =============================================================================
bool findCardByUID(const String& uid, String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      keyHex = card["key"].as<String>();
      return true;
    }
  }
  return false;
}

bool isCardRegistered(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      return true;
    }
  }
  return false;
}

bool addCardToDatabase(const String& uid, const String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  JsonObject newCard = cards.add<JsonObject>();
  newCard["uid"] = uid;
  newCard["key"] = keyHex;
  saveCards();
  return true;
}

bool removeCardFromDatabase(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (size_t i = 0; i < cards.size(); i++) {
    if (cards[i]["uid"] == uid) {
      cards.remove(i);
      saveCards();
      return true;
    }
  }
  return false;
}

// =============================================================================
// 鉴权函数
// =============================================================================
bool performAuthentication(const String& uid) {
  Serial.print("Authenticating card: ");
  Serial.println(uid);

  // Find card in database
  String keyHex;
  if (!findCardByUID(uid, keyHex)) {
    Serial.println("Card not registered");
    return false;
  }

  // Get stored key and authenticate
  uint8_t key[KEY_SIZE];
  hexStringToKey(keyHex, key);

  uint8_t uidBytes[7], uidLen;
  if (!nfc.readDetectedPassiveTargetID(uidBytes, &uidLen)) {
    Serial.println("Failed to read card UID");
    return false;
  }

  if (authenticateBlock(uidBytes, uidLen, AUTH_BLOCK, key)) {
    Serial.println("Authentication successful");
    blinkLED(3, 100);  // Success blink
    return true;
  } else {
    Serial.println("Authentication failed");
    blinkLED(1, 500);  // Failure blink
    return false;
  }
}

void performCardRegistration(const String& uid) {
  Serial.print("Registering card with UID: ");
  Serial.println(uid);

  // Check if card is already registered
  if (isCardRegistered(uid)) {
    Serial.println("Card already registered");
    return;
  }

  // Get UID bytes for authentication
  uint8_t uidBytes[7], uidLen;
  if (!nfc.readDetectedPassiveTargetID(uidBytes, &uidLen)) {
    Serial.println("Failed to read card UID");
    return;
  }

  // Authenticate with default key
  if (!authenticateBlock(uidBytes, uidLen, SECTOR_TRAILER_BLOCK, defaultKey)) {
    Serial.println("Authentication with default key failed");
    return;
  }

  // Generate and write new key
  uint8_t newKey[KEY_SIZE];
  generateRandomKey(newKey);

  if (!writeSectorTrailer(newKey)) {
    Serial.println("Failed to write sector trailer");
    return;
  }

  // Add to database
  String keyHex = keyToHexString(newKey);
  if (addCardToDatabase(uid, keyHex)) {
    Serial.println("Card registered successfully");
    blinkLED(5, 100);  // Registration success blink
  } else {
    Serial.println("Failed to save card to database");
  }
}

// =============================================================================
// 状态机处理函数
// =============================================================================
void handleWaitingForCard() {
  irqCurrent = digitalRead(PN532_IRQ);

  if (irqCurrent == LOW && irqPrevious == HIGH) {
    Serial.println("Card detected!");
    String uid = readCurrentCardUID();
    if (uid.length() > 0) {
      cardInfo.lastUID = uid;
      cardInfo.lastDetectionTime = millis();
      setState(AUTHENTICATING);
    }
  }

  irqPrevious = irqCurrent;
}

void handleAuthenticating() {
  if (cardInfo.lastUID.length() > 0) {
    performAuthentication(cardInfo.lastUID);

    // Enter cooldown and start detection immediately
    cardInfo.cooldownStartTime = millis();
    setState(COOLDOWN);
    startNFCDetection();
  }
}

void handleCooldown() {
  irqCurrent = digitalRead(PN532_IRQ);

  if (irqCurrent == LOW && irqPrevious == HIGH) {
    String currentUID = readCurrentCardUID();

    if (currentUID.length() > 0) {
      if (currentUID == cardInfo.lastUID) {
        // Same UID: reset cooldown timer
        Serial.println("Same card detected during cooldown, resetting timer");
        cardInfo.cooldownStartTime = millis();
        delay(SAME_CARD_DELAY);
        startNFCDetection();
      } else {
        // Different UID: bypass cooldown
        Serial.println("Different card detected, bypassing cooldown");
        cardInfo.lastUID = currentUID;
        cardInfo.lastDetectionTime = millis();
        setState(AUTHENTICATING);
      }
    }
  } else {
    // Check cooldown timeout
    if (millis() - cardInfo.cooldownStartTime > COOLDOWN_DURATION) {
      Serial.println("Cooldown completed");
      setState(WAITING_FOR_CARD);
      startNFCDetection();
    }
  }

  irqPrevious = irqCurrent;
}

void handleRegistrationMode() {
  irqCurrent = digitalRead(PN532_IRQ);

  if (irqCurrent == LOW && irqPrevious == HIGH) {
    Serial.println("Card detected for registration!");
    String uid = readCurrentCardUID();
    if (uid.length() > 0) {
      cardInfo.lastUID = uid;
      setState(REGISTERING);
    }
  } else {
    // Check registration timeout
    if (millis() - cardInfo.registrationStartTime > REGISTRATION_TIMEOUT) {
      Serial.println("Registration timeout, returning to normal mode");
      setState(WAITING_FOR_CARD);
      startNFCDetection();
    }
  }

  irqPrevious = irqCurrent;
}

void handleRegistering() {
  if (cardInfo.lastUID.length() > 0) {
    performCardRegistration(cardInfo.lastUID);
    setState(WAITING_FOR_CARD);
    startNFCDetection();
  }
}

// =============================================================================
// 命令处理
// =============================================================================
void handleRegisterCommand() {
  Serial.println("-- Registration mode activated --");
  Serial.println("-- Tap blank card within 10 seconds --");

  cardInfo.registrationStartTime = millis();
  setState(REGISTRATION_MODE);
  startNFCDetection();
}

void handleListCommand() {
  Serial.println("-- Registered Cards --");
  JsonArray cards = cardDatabase.as<JsonArray>();
  
  if (cards.size() == 0) {
    Serial.println("No cards registered");
    return;
  }

  for (JsonObject card : cards) {
    Serial.print(card["uid"].as<const char*>());
    Serial.print(" : ");
    Serial.println(card["key"].as<const char*>());
  }
}

void handleDeleteCommand(const String& uid) {
  if (uid.length() == 0) {
    Serial.println("Usage: del <UID>");
    return;
  }

  if (removeCardFromDatabase(uid)) {
    Serial.println("Deleted " + uid);
  } else {
    Serial.println("Card not found: " + uid);
  }
}

void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command.equalsIgnoreCase("reg")) {
    handleRegisterCommand();
  }
  else if (command.equalsIgnoreCase("list")) {
    handleListCommand();
  }
  else if (command.startsWith("del")) {
    String uid = command.substring(3);
    uid.trim();
    handleDeleteCommand(uid);
  }
  else {
    Serial.println("Unknown command");
    Serial.println("Available commands: reg, list, del <UID>");
    Serial.println("Note: Authentication is automatic when cards are detected");
  }
}

// =============================================================================
// 初始化
// =============================================================================
bool initializeFileSystem() {
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
    return false;
  }
  loadCards();
  return true;
}

bool initializeNFC() {
  Serial.println("=== PN532 初始化诊断 ===");

  // 硬件重置
  Serial.println("1. 执行硬件重置...");
  digitalWrite(PN532_RESET, LOW);
  delay(100);
  digitalWrite(PN532_RESET, HIGH);
  delay(500);

  // 初始化I2C
  Serial.println("2. 初始化I2C (100kHz)...");
  Wire.begin();
  Wire.setClock(100000); // 降低时钟频率到100kHz

  // 扫描I2C设备
  Serial.println("3. 扫描I2C总线...");
  bool deviceFound = false;
  for(byte addr = 1; addr < 127; addr++) {
    Wire.beginTransmission(addr);
    if(Wire.endTransmission() == 0) {
      Serial.print("   发现设备: 0x");
      Serial.println(addr, HEX);
      if(addr == 0x24) {
        Serial.println("   ✅ 这是PN532地址!");
        deviceFound = true;
      }
    }
  }

  if(!deviceFound) {
    Serial.println("   ❌ 未发现PN532设备 (0x24)");
    Serial.println("   请检查硬件连接!");
    return false;
  }

  // 初始化PN532库
  Serial.println("4. 初始化PN532库...");
  nfc.begin();
  delay(200);

  // 尝试获取固件版本
  Serial.println("5. 获取固件版本...");
  uint32_t version = 0;
  for (int retry = 0; retry < 5; retry++) {
    Serial.print("   尝试 ");
    Serial.print(retry + 1);
    Serial.print("/5... ");

    version = nfc.getFirmwareVersion();
    if (version) {
      Serial.println("成功!");
      break;
    }
    Serial.println("失败");
    delay(1000);
  }

  if (!version) {
    Serial.println("   ❌ 无法获取固件版本");
    return false;
  }

  Serial.print("   ✅ 固件版本: 0x");
  Serial.println(version, HEX);

  // 配置SAM
  Serial.println("6. 配置SAM...");
  if (!nfc.SAMConfig()) {
    Serial.println("   ❌ SAM配置失败");
    return false;
  }

  Serial.println("   ✅ SAM配置成功");
  Serial.println("=== PN532 初始化完成 ===");
  return true;
}

bool checkNFCHealth() {
  // Check if we've had too many consecutive errors
  if (consecutiveErrors > 5) {
    Serial.println("Too many NFC errors, attempting recovery...");

    // Try to reinitialize NFC
    if (initializeNFC()) {
      consecutiveErrors = 0;
      lastSuccessfulOperation = millis();
      Serial.println("NFC recovery successful");
      return true;
    } else {
      Serial.println("NFC recovery failed");
      return false;
    }
  }
  return true;
}

void initializeLED() {
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, LOW);
}

void printWelcomeMessage() {
  Serial.println("=================================");
  Serial.println("    Door Access System Ready    ");
  Serial.println("=================================");
  Serial.println("Commands:");
  Serial.println("  reg       - Register new card");
  Serial.println("  list      - List all cards");
  Serial.println("  del <UID> - Delete card");
  Serial.println("=================================");
  Serial.println("System will automatically detect");
  Serial.println("and authenticate cards.");
  Serial.println("=================================");
}

// =============================================================================
// 主函数
// =============================================================================
void setup() {
  initializeLED();

  Serial.begin(115200);
  for (int i = 0; i < 3; i++) {
    blinkLED(1, 100);
    delay(100);
  }

  if (!initializeFileSystem()) {
    Serial.println("Failed to initialize file system");
    while (true) delay(1000);
  }

  if (!initializeNFC()) {
    Serial.println("Failed to initialize NFC reader");
    while (true) delay(1000);
  }

  printWelcomeMessage();

  // Initialize state machine
  setState(WAITING_FOR_CARD);
  startNFCDetection();
}

void loop() {
  // Handle serial commands
  if (Serial.available()) {
    processSerialCommand();
  }

  // State machine processing
  switch (currentState) {
    case WAITING_FOR_CARD:
      handleWaitingForCard();
      break;

    case AUTHENTICATING:
      handleAuthenticating();
      break;

    case COOLDOWN:
      handleCooldown();
      break;

    case REGISTRATION_MODE:
      handleRegistrationMode();
      break;

    case REGISTERING:
      handleRegistering();
      break;
  }

  // Small delay to prevent excessive CPU usage
  delay(1);
}
